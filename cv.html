    <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Fady <PERSON> - Computer Teacher CV</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      color: #333;
    }
    .container {
      max-width: 900px;
      margin: 0 auto;
      padding: 30px;
      background-color: white;
      box-shadow: 0 0 20px rgba(0,0,0,0.1);
    }
    h1 {
      color: #2c3e50;
      margin-bottom: 5px;
    }
    h3 {
      color: #3498db;
      margin-top: 0;
      font-weight: 400;
    }
    h2 {
      color: #2c3e50;
      border-bottom: 2px solid #3498db;
      padding-bottom: 5px;
    }
    a {
      color: #3498db;
      text-decoration: none;
      transition: color 0.3s;
    }
    a:hover {
      color: #2980b9;
      text-decoration: underline;
    }
    .section {
      margin-bottom: 25px;
    }
    .project {
      margin-bottom: 15px;
      padding-left: 10px;
      border-left: 3px solid #3498db;
    }
    ul {
      padding-left: 20px;
    }
    li {
      margin-bottom: 5px;
    }
    .print-button {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background-color: #3498db;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 5px;
      cursor: pointer;
      box-shadow: 0 2px 5px rgba(0,0,0,0.2);
      transition: all 0.3s;
      z-index: 1000;
    }
    
    .print-button:hover {
      background-color: #2980b9;
    }
    
    @media print {
      .print-button {
        display: none;
      }
      body {
        background-color: white;
      }
      .container {
        box-shadow: none;
        padding: 15px;
      }
      /* إضافة هذه الأنماط لإخفاء عنوان الصفحة عند الطباعة */
      @page {
        margin: 0;
        size: auto;
      }
      @page :first {
        margin-top: 0;
      }
      head, title {
        display: none;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Fady Ezzat Garras Wassef</h1>
    <h3>Computer Teacher</h3>

  <div class="section">
    <h2>Contact</h2>
    <p><strong>Phone:</strong> +20 ************</p>
    <p><strong>Email:</strong> <EMAIL></p>
    <p><strong>GitHub:</strong> <a href="https://github.com/fady-ezzat-garras">github.com/fady-ezzat-garras</a></p>
    <p><strong>LinkedIn:</strong> <a href="https://linkedin.com/in/fady-ezzat-garras">linkedin.com/in/fady-ezzat-garras</a></p>
    <p><strong>Location:</strong> Assiut, Egypt</p>
  </div>

  <div class="section">
    <h2>Profile</h2>
    <p>A Computer Teacher who combines technical proficiency with the ability to simplify concepts for students. Holds a strong background in Information Technology and web development. Experienced in teaching basic computer skills and programming concepts using languages such as Python and Scratch for children. Proficient in Microsoft Office and integrating internet tools into education. Communicates well in English and is committed to creating a positive, supportive learning environment. Open to adopting new educational technologies and working collaboratively with colleagues to support student development.</p>
  </div>

  <div class="section">
    <h2>Education</h2>
    <p><strong>Oct 2024 – July 2025:</strong> 9-Month Professional Diploma, Open-Source Track – ITI Assiut Branch</p>
    <p><strong>May 2024 – Sep 2024:</strong> 3-Month Software Development Fundamentals – ITI Sohag Branch</p>
    <p><strong>Jun 2018 – Jun 2022:</strong> Faculty of Specific Education, Department of IT – Grade: Very Good</p>
  </div>

  <div class="section">
    <h2>Teaching Qualifications & Competencies</h2>
    <ul>
      <li><strong>Educational Background:</strong> Bachelor's degree in Information Technology from Faculty of Specific Education</li>
      <li><strong>Technical Expertise:</strong> 1-3 years of hands-on experience in computer studies and programming</li>
      <li><strong>Teaching Skills:</strong> Ability to manage students effectively and maintain positive learning environments</li>
      <li><strong>Communication:</strong> Strong verbal and written communication skills in English (B1 level)</li><br>
      <li><strong>Lesson Planning:</strong> Capable of preparing engaging and age-appropriate computer lessons</li>
      <li><strong>Technology Integration:</strong> Willingness to learn and integrate new educational technologies</li>
      <li><strong>Collaboration:</strong> Cooperative and flexible in working with other teachers and staff</li>
      <li><strong>Core Competencies:</strong> Proficient in Microsoft Office, internet applications, and basic programming concepts</li>
    </ul>
  </div>

  <div class="section">
    <h2>Projects</h2>

    <div class="project">
      <strong>Dibla Smart AI:</strong> Participated in implementing an electronic platform for gold shops using AI and chatbots.
      <br><a href="https://github.com/mohamed8112001/Gold-Frontend">View on GitHub</a>
    </div>

    <div class="project">
      <strong>Charitable Organization:</strong> A front-end app built using HTML, CSS, JavaScript (ES6).
      <br><a href="https://github.com/fady-ezzat-garras/Charitable-Organization">View on GitHub</a>
    </div>

    <div class="project">
      <strong>Laravel-Job-Board:</strong> Developed a full-featured job board platform using Laravel with user roles, job posts, and admin panel.
      <br><a href="https://github.com/fady-ezzat-garras/Laravel-Job-Board">View on GitHub</a>
    </div>
  </div>

  <div class="section">
    <h2>Work Experience</h2>
    <ul>
      <li>Custom Theme Design for Salla Platform – Khamsat, Apr 2025 – <a href="https://khamsat.com/user/fadyezzat321/reviews/1053164">Review & Rating</a></li>
      <li>Custom Theme Design for Salla Platform – Khamsat, Mar 2025 – <a href="https://khamsat.com/user/fadyezzat321/reviews/1053095">Review & Rating </a></li>
      <li>Custom Theme Design and Development for Salla – Mostaql, Mar 2025 – <a href="https://mostaql.com/u/FadyEzzat_foda/reviews/8637070">Review & Rating</a></li>
      <li>Multi-Vendor Store – WordPress & WooCommerce – Mostaql, Jan 2025 – <a href="https://mostaql.com/u/FadyEzzat_foda/reviews/8390886">Review & Rating</a></li>
    </ul>
  </div>

  <div class="section">
    <h2>Technical Skills</h2>
    <p><strong>Microsoft Office Suite:</strong> Word, Excel, PowerPoint, Access - Advanced proficiency</p>
    <p><strong>Programming Concepts:</strong> HTML5, CSS3, JavaScript, PHP, Python - Teaching and practical application</p>
    <p><strong>Internet Applications:</strong> Web browsers, email systems, online collaboration tools, educational platforms</p>
    <p><strong>Database Management:</strong> MySQL, SQL basics for educational purposes</p>
    <p><strong>Development Tools:</strong> Git, Visual Studio Code, basic Linux commands</p>
    <p><strong>Educational Technology:</strong> Learning Management Systems, interactive presentation tools</p>
    <p><strong>Web Technologies:</strong> React, Vue, Angular, Laravel, Node.js, WordPress</p>
  </div>

  <div class="section"><br>
    <h2>Languages</h2>
    <p><strong>Arabic:</strong> Native</p>
    <p><strong>English:</strong> B1 (Intermediate)</p>
  </div>

  <div class="section">
    <h2>Additional Information</h2>
    <p><strong>Nationality:</strong> Egyptian</p>
    <p><strong>Date of Birth:</strong> Jan 16, 2000</p>
    <p><strong>Military Status:</strong> Completed</p>
  </div>

  <div class="section">
    <h2>Teaching & Soft Skills</h2>
    <ul>
      <li>Effective student management and classroom control</li>
      <li>Strong verbal and written communication in English</li>
      <li>Engaging lesson preparation and delivery</li>
      <li>Collaborative teamwork with teaching staff</li>
      <li>Flexibility and adaptability in educational environments</li>
      <li>Time management and deadline adherence</li>
      <li>Quick learner with enthusiasm for new educational technologies</li>
      <li>Problem-solving and critical thinking skills</li>
      <li>Patience and empathy in student interactions</li>
    </ul>
  </div>
  </div>
  <button class="print-button" onclick="window.print()">تنزيل كـ PDF</button>
</body>
<script>
  // يمكنك إضافة هذا السكريبت لتحسين تجربة الطباعة
  document.querySelector('.print-button').addEventListener('click', function() {
    window.print();
  });
</script>
</html>
