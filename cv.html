    <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Fady Ezzat - Back-End CV</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      color: #333;
    }
    .container {
      max-width: 900px;
      margin: 0 auto;
      padding: 30px;
      background-color: white;
      box-shadow: 0 0 20px rgba(0,0,0,0.1);
    }
    h1 {
      color: #2c3e50;
      margin-bottom: 5px;
    }
    h3 {
      color: #3498db;
      margin-top: 0;
      font-weight: 400;
    }
    h2 {
      color: #2c3e50;
      border-bottom: 2px solid #3498db;
      padding-bottom: 5px;
    }
    a {
      color: #3498db;
      text-decoration: none;
      transition: color 0.3s;
    }
    a:hover {
      color: #2980b9;
      text-decoration: underline;
    }
    .section {
      margin-bottom: 25px;
    }
    .project {
      margin-bottom: 15px;
      padding-left: 10px;
      border-left: 3px solid #3498db;
    }
    ul {
      padding-left: 20px;
    }
    li {
      margin-bottom: 5px;
    }
    .print-button {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background-color: #3498db;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 5px;
      cursor: pointer;
      box-shadow: 0 2px 5px rgba(0,0,0,0.2);
      transition: all 0.3s;
      z-index: 1000;
    }
    
    .print-button:hover {
      background-color: #2980b9;
    }
    
    @media print {
      .print-button {
        display: none;
      }
      body {
        background-color: white;
      }
      .container {
        box-shadow: none;
        padding: 15px;
      }
      /* إضافة هذه الأنماط لإخفاء عنوان الصفحة عند الطباعة */
      @page {
        margin: 0;
        size: auto;
      }
      @page :first {
        margin-top: 0;
      }
      head, title {
        display: none;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Fady Ezzat Garras Wassef</h1>
    <h3>Back-End Developer</h3>

  <div class="section">
    <h2>Contact</h2>
    <p><strong>Phone:</strong> +20 ************</p>
    <p><strong>Email:</strong> <EMAIL></p>
    <p><strong>GitHub:</strong> <a href="https://github.com/fady-ezzat-garras">github.com/fady-ezzat-garras</a></p>
    <p><strong>LinkedIn:</strong> <a href="https://linkedin.com/in/fady-ezzat-garras">linkedin.com/in/fady-ezzat-garras</a></p>
    <p><strong>Location:</strong> Assiut, Egypt</p>
  </div>

  <div class="section">
    <h2>Profile</h2>
    <p>Detail-oriented and self-motivated web developer with a strong foundation in both front-end and back-end technologies. Experienced in freelance project delivery using modern tech stacks and open-source frameworks. Adept at creating responsive, scalable, and efficient web applications with a focus on performance and user experience.</p>
  </div>

  <div class="section">
    <h2>Education</h2>
    <p><strong>Oct 2024 – July 2025:</strong> 9-Month Professional Diploma, Open-Source Track – ITI Assiut Branch</p>
    <p><strong>May 2024 – Sep 2024:</strong> 3-Month Software Development Fundamentals – ITI Sohag Branch</p>
    <p><strong>Jun 2018 – Jun 2022:</strong> Faculty of Specific Education, Department of IT – Grade: Very Good</p>
  </div>

  <div class="section">
    <h2>Projects</h2>

    <div class="project">
      <strong>Coffee PHP Project:</strong> A dynamic e-commerce website for a coffee shop using PHP and MySQL.  
      <br><a href="https://github.com/mohamed8112001/PHP-project-">View on GitHub</a>
    </div>

    <div class="project">
      <strong>Charitable Organization:</strong> A front-end app built using HTML, CSS, JavaScript (ES6).  
      <br><a href="https://github.com/fady-ezzat-garras/Charitable-Organization">View on GitHub</a>
    </div>


    <div class="project">
      <strong>Laravel-Job-Board:</strong> Developed a full-featured job board platform using Laravel with user roles, job posts, and admin panel.  
      <br><a href="https://github.com/fady-ezzat-garras/Laravel-Job-Board">View on GitHub</a>
    </div>
  </div>

  <div class="section">
    <h2>Work Experience</h2>
    <ul>
      <li>Custom Theme Design for Salla Platform – Khamsat, Apr 2025 – <a href="https://khamsat.com/user/fadyezzat321/reviews/1053164">Review & Rating</a></li>
      <li>Custom Theme Design for Salla Platform – Khamsat, Mar 2025 – <a href="https://khamsat.com/user/fadyezzat321/reviews/1053095">Review & Rating </a></li>
      <li>Custom Theme Design and Development for Salla – Mostaql, Mar 2025 – <a href="https://mostaql.com/u/FadyEzzat_foda/reviews/8637070">Review & Rating</a></li>
      <li>Multi-Vendor Store – WordPress & WooCommerce – Mostaql, Jan 2025 – <a href="https://mostaql.com/u/FadyEzzat_foda/reviews/8390886">Review & Rating</a></li>
    </ul>
  </div>

  <div class="section">
    <h2>Skills</h2>
    <p><strong>Front-End:</strong> HTML5, CSS3, JavaScript (ES6+), React, Vue, Angular, Tailwind, Bootstrap</p>
    <p><strong>Back-End:</strong> PHP, Laravel, Node.js, WordPress, Python</p>
    <p><strong>Databases:</strong> MySQL, MongoDB, SQL</p>
    <p><strong>Tools:</strong> Git, Bash, Linux, Docker, JWT, OAuth</p>
  </div>

  <div class="section">
    <h2>Languages</h2>
    <p><strong>Arabic:</strong> Native</p>
    <p><strong>English:</strong> B1 (Intermediate)</p>
  </div>

  <div class="section">
    <h2>Additional Information</h2>
    <p><strong>Nationality:</strong> Egyptian</p>
    <p><strong>Date of Birth:</strong> Jan 16, 2000</p>
    <p><strong>Military Status:</strong> Completed</p>
  </div>

  <div class="section">
    <h2>Soft Skills</h2>
    <ul>
      <li>Strong team collaboration and communication</li>
      <li>Respects deadlines and time management</li>
      <li>Quick and adaptive learner</li>
      <li>Problem-solving and critical thinking</li>
    </ul>
  </div>
  </div>
  <button class="print-button" onclick="window.print()">تنزيل كـ PDF</button>
</body>
<script>
  // يمكنك إضافة هذا السكريبت لتحسين تجربة الطباعة
  document.querySelector('.print-button').addEventListener('click', function() {
    window.print();
  });
</script>
</html>
